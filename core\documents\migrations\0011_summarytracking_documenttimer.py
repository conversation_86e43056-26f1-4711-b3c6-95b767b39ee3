# Generated by Django 4.2.21 on 2025-06-18 05:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('documents', '0010_rename_questionanswer_quiz'),
    ]

    operations = [
        migrations.CreateModel(
            name='SummaryTracking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('summary_generated_at', models.DateTimeField(auto_now_add=True)),
                ('reached_end', models.BooleanField(default=False, help_text='User scrolled to end of summary')),
                ('reached_end_at', models.DateTimeField(blank=True, null=True)),
                ('stayed_avg_time', models.BooleanField(default=False, help_text='User stayed >= average read time')),
                ('stayed_avg_time_at', models.DateTimeField(blank=True, null=True)),
                ('reopened_once', models.BooleanField(default=False, help_text='User reopened summary at least once')),
                ('reopened_at', models.DateTimeField(blank=True, null=True)),
                ('first_view_time', models.DateTimeField(blank=True, null=True)),
                ('total_view_time_minutes', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('predicted_avg_time_minutes', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('view_count', models.IntegerField(default=0, help_text='Number of times summary was viewed')),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='summary_tracking', to='documents.document')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='summary_tracking', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-summary_generated_at'],
                'indexes': [models.Index(fields=['document', 'user'], name='documents_s_documen_e5b56d_idx')],
            },
        ),
        migrations.CreateModel(
            name='DocumentTimer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_start_time', models.DateTimeField(help_text='When the timer session started')),
                ('session_end_time', models.DateTimeField(blank=True, help_text='When the timer session ended', null=True)),
                ('pause_time', models.DateTimeField(blank=True, help_text='When the timer was paused', null=True)),
                ('resume_time', models.DateTimeField(blank=True, help_text='When the timer was resumed', null=True)),
                ('total_time_minutes', models.DecimalField(decimal_places=2, default=0, help_text='Total time in minutes', max_digits=10)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this timer session is currently active')),
                ('ended_by_quiz', models.BooleanField(default=False, help_text='Whether this session ended by starting a quiz')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='timer_sessions', to='documents.document')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='timer_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['document', 'user', 'is_active'], name='documents_d_documen_a6492c_idx')],
            },
        ),
    ]
