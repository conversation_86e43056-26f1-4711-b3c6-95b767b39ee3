#!/usr/bin/env python3
"""
Verify that all timer and summary tracking endpoints are properly configured
"""

import os
import sys
import django

# Add the core directory to the path
core_path = os.path.join(os.path.dirname(__file__), 'core')
sys.path.insert(0, core_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.urls import reverse, NoReverseMatch
from django.test import RequestFactory
from django.contrib.auth import get_user_model
from documents.models import Document

User = get_user_model()

def test_url_patterns():
    """Test that all our new URL patterns are properly configured"""
    print("🔄 Testing URL Patterns...")
    
    # Test document ID for URL patterns
    test_doc_id = 1
    
    # Timer endpoints
    timer_endpoints = [
        ('start-timer', f'/api/documents/{test_doc_id}/timer/start/'),
        ('pause-timer', f'/api/documents/{test_doc_id}/timer/pause/'),
        ('resume-timer', f'/api/documents/{test_doc_id}/timer/resume/'),
        ('stop-timer', f'/api/documents/{test_doc_id}/timer/stop/'),
    ]
    
    # Summary tracking endpoints
    summary_endpoints = [
        ('track-summary-event', f'/api/documents/{test_doc_id}/summary/track/'),
        ('calculate-avg-read-time', f'/api/documents/{test_doc_id}/summary/avg-time/'),
    ]
    
    # Test endpoints
    test_endpoints = [
        ('test-file-creation', f'/api/documents/{test_doc_id}/test-files/'),
    ]
    
    all_endpoints = timer_endpoints + summary_endpoints + test_endpoints
    
    for name, expected_url in all_endpoints:
        try:
            url = reverse(name, kwargs={'document_id': test_doc_id})
            if url == expected_url:
                print(f"✅ {name}: {url}")
            else:
                print(f"⚠️  {name}: Expected {expected_url}, got {url}")
        except NoReverseMatch as e:
            print(f"❌ {name}: URL pattern not found - {e}")
    
    return True

def test_api_imports():
    """Test that all API functions can be imported"""
    print("\n🔄 Testing API Function Imports...")
    
    try:
        from documents.api import (
            start_timer, pause_timer, resume_timer, stop_timer,
            track_summary_event, calculate_avg_read_time,
            create_timing_file, create_summary_tracking_file,
            test_file_creation
        )
        print("✅ All API functions imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import API functions: {e}")
        return False

def test_model_imports():
    """Test that all models can be imported and have expected fields"""
    print("\n🔄 Testing Model Imports...")
    
    try:
        from documents.models import DocumentTimer, SummaryTracking
        
        # Test DocumentTimer fields
        timer_fields = [f.name for f in DocumentTimer._meta.fields]
        expected_timer_fields = [
            'id', 'document', 'user', 'session_start_time', 'session_end_time',
            'pause_time', 'resume_time', 'total_time_minutes', 'is_active',
            'ended_by_quiz', 'created_at', 'updated_at'
        ]
        
        missing_timer_fields = set(expected_timer_fields) - set(timer_fields)
        if missing_timer_fields:
            print(f"❌ DocumentTimer missing fields: {missing_timer_fields}")
            return False
        else:
            print("✅ DocumentTimer model has all expected fields")
        
        # Test SummaryTracking fields
        tracking_fields = [f.name for f in SummaryTracking._meta.fields]
        expected_tracking_fields = [
            'id', 'document', 'user', 'summary_generated_at', 'reached_end',
            'reached_end_at', 'stayed_avg_time', 'stayed_avg_time_at',
            'reopened_once', 'reopened_at', 'first_view_time',
            'total_view_time_minutes', 'predicted_avg_time_minutes', 'view_count'
        ]
        
        missing_tracking_fields = set(expected_tracking_fields) - set(tracking_fields)
        if missing_tracking_fields:
            print(f"❌ SummaryTracking missing fields: {missing_tracking_fields}")
            return False
        else:
            print("✅ SummaryTracking model has all expected fields")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import models: {e}")
        return False

def test_frontend_api_functions():
    """Test that frontend API functions are properly structured"""
    print("\n🔄 Testing Frontend API Structure...")
    
    # Read the frontend API file
    frontend_api_path = os.path.join(os.path.dirname(__file__), 'frontend', 'lib', 'api.ts')
    
    if not os.path.exists(frontend_api_path):
        print(f"❌ Frontend API file not found: {frontend_api_path}")
        return False
    
    with open(frontend_api_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for timer API functions
    timer_functions = ['startTimer', 'pauseTimer', 'resumeTimer', 'stopTimer']
    for func in timer_functions:
        if func in content:
            print(f"✅ Found timer function: {func}")
        else:
            print(f"❌ Missing timer function: {func}")
    
    # Check for summary tracking API functions
    tracking_functions = ['trackEvent', 'calculateAvgReadTime']
    for func in tracking_functions:
        if func in content:
            print(f"✅ Found tracking function: {func}")
        else:
            print(f"❌ Missing tracking function: {func}")
    
    # Check for API exports
    if 'timerApi' in content:
        print("✅ Found timerApi export")
    else:
        print("❌ Missing timerApi export")
    
    if 'summaryTrackingApi' in content:
        print("✅ Found summaryTrackingApi export")
    else:
        print("❌ Missing summaryTrackingApi export")
    
    return True

def main():
    """Main verification function"""
    print("🚀 Starting Endpoint and Implementation Verification")
    print("=" * 60)
    
    success = True
    
    # Test URL patterns
    if not test_url_patterns():
        success = False
    
    # Test API imports
    if not test_api_imports():
        success = False
    
    # Test model imports
    if not test_model_imports():
        success = False
    
    # Test frontend API structure
    if not test_frontend_api_functions():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All verifications passed!")
        print("✅ URL patterns are properly configured")
        print("✅ API functions are properly implemented")
        print("✅ Models are properly defined")
        print("✅ Frontend API structure is correct")
    else:
        print("❌ Some verifications failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
