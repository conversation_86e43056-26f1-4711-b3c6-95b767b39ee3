from django.db import models
from django.utils import timezone
from users.models import Student

class Document(models.Model):
    PROCESSING_STATUS = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ]

    user = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='documents')
    title = models.CharField(max_length=255)
    file = models.FileField(upload_to='documents/')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    processing_status = models.CharField(max_length=20, choices=PROCESSING_STATUS, default='pending')
    error_message = models.TextField(null=True, blank=True)
    blueprint = models.TextField(null=True, blank=True, help_text="Blueprint for the document")

    def __str__(self):
        return f"{self.title} ({self.get_processing_status_display()})"

class DocumentEmbedding(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='embeddings')
    text_chunk = models.TextField(help_text="The text segment this embedding represents")
    embedding = models.JSONField(help_text="Vector embedding of the text chunk")
    chunk_number = models.IntegerField(help_text="Order of this chunk in the document")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['chunk_number']
        indexes = [
            models.Index(fields=['document', 'chunk_number']),
        ]

    def __str__(self):
        return f"{self.document.title} - Chunk {self.chunk_number}"

class Flashcard(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    front = models.TextField()
    back = models.TextField()

class Flowchart(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    mermaid_code = models.TextField(help_text="Store Mermaid.js flowchart code")

class Quiz(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    question = models.TextField()
    answer = models.TextField()

class BlueprintTopics(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='topics')
    title = models.CharField(max_length=255, help_text="Title of the topic")
    weightage = models.DecimalField(max_digits=5, decimal_places=2, help_text="Weightage of the topic in percentile")
    content = models.ManyToManyField(DocumentEmbedding, related_name='topics', help_text="Relevant document embeddings for this topic")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Blueprint Topic"
        verbose_name_plural = "Blueprint Topics"
        ordering = ['-weightage']

    def __str__(self):
        return f"{self.title} ({self.weightage}%)"


class DocumentTimer(models.Model):
    """Track timing sessions for documents - from 2nd webpage load to quiz start"""
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='timer_sessions')
    user = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='timer_sessions')
    session_start_time = models.DateTimeField(help_text="When the timer session started")
    session_end_time = models.DateTimeField(null=True, blank=True, help_text="When the timer session ended")
    pause_time = models.DateTimeField(null=True, blank=True, help_text="When the timer was paused")
    resume_time = models.DateTimeField(null=True, blank=True, help_text="When the timer was resumed")
    total_time_minutes = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Total time in minutes")
    is_active = models.BooleanField(default=True, help_text="Whether this timer session is currently active")
    ended_by_quiz = models.BooleanField(default=False, help_text="Whether this session ended by starting a quiz")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['document', 'user', 'is_active']),
        ]

    def __str__(self):
        return f"Timer for {self.document.title} - {self.total_time_minutes} min"

    def calculate_total_time(self):
        """Calculate total time in minutes for this session"""
        if not self.session_end_time:
            return 0

        total_seconds = (self.session_end_time - self.session_start_time).total_seconds()
        return round(total_seconds / 60, 2)

    def save(self, *args, **kwargs):
        if self.session_end_time and self.session_start_time:
            self.total_time_minutes = self.calculate_total_time()
        super().save(*args, **kwargs)


class SummaryTracking(models.Model):
    """Track user interactions with document summaries"""
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='summary_tracking')
    user = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='summary_tracking')
    summary_generated_at = models.DateTimeField(auto_now_add=True)

    # Tracking flags
    reached_end = models.BooleanField(default=False, help_text="User scrolled to end of summary")
    reached_end_at = models.DateTimeField(null=True, blank=True)

    stayed_avg_time = models.BooleanField(default=False, help_text="User stayed >= average read time")
    stayed_avg_time_at = models.DateTimeField(null=True, blank=True)

    reopened_once = models.BooleanField(default=False, help_text="User reopened summary at least once")
    reopened_at = models.DateTimeField(null=True, blank=True)

    # Time tracking
    first_view_time = models.DateTimeField(null=True, blank=True)
    total_view_time_minutes = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    predicted_avg_time_minutes = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Session tracking
    view_count = models.IntegerField(default=0, help_text="Number of times summary was viewed")

    class Meta:
        ordering = ['-summary_generated_at']
        indexes = [
            models.Index(fields=['document', 'user']),
        ]
        constraints = [
            models.UniqueConstraint(fields=['document', 'user'], name='unique_summary_tracking_per_user_document')
        ]

    def __str__(self):
        return f"Summary tracking for {self.document.title}"