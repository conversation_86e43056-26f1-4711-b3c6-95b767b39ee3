#!/usr/bin/env python3
"""
Test summary tracking functionality after fixes
"""

import os
import sys
import django

# Add the core directory to the path
core_path = os.path.join(os.path.dirname(__file__), 'core')
sys.path.insert(0, core_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from documents.models import Document, SummaryTracking
from django.utils import timezone
from documents.api import create_summary_tracking_file
from decimal import Decimal

User = get_user_model()

def test_summary_tracking_creation():
    """Test that summary tracking works without duplicates"""
    print("🔄 Testing Summary Tracking Creation...")
    
    # Get or create test user
    user, created = User.objects.get_or_create(
        username='summary_test_user',
        defaults={'email': '<EMAIL>'}
    )
    print(f"✅ Using user: {user.username}")
    
    # Get or create test document
    document, created = Document.objects.get_or_create(
        title='Summary Test Document',
        user=user,
        defaults={'processing_status': 'completed'}
    )
    print(f"✅ Using document: {document.title}")
    
    # Clean up existing tracking
    SummaryTracking.objects.filter(document=document, user=user).delete()
    print("🧹 Cleaned up existing tracking")
    
    # Test the fixed tracking logic
    print("\n📍 Testing tracking record creation...")
    
    # Simulate the fixed get_or_create logic
    tracking = SummaryTracking.objects.filter(
        document=document,
        user=user
    ).first()
    
    if not tracking:
        tracking = SummaryTracking.objects.create(
            document=document,
            user=user,
            first_view_time=timezone.now()
        )
        print("✅ Created new tracking record")
    else:
        print("✅ Found existing tracking record")
    
    # Test updating tracking with events
    current_time = timezone.now()
    
    # Simulate "reached end" event
    tracking.reached_end = True
    tracking.reached_end_at = current_time
    tracking.save()
    print("✅ Added 'reached end' event")
    
    # Simulate "stayed avg time" event
    tracking.stayed_avg_time = True
    tracking.stayed_avg_time_at = current_time
    tracking.predicted_avg_time_minutes = Decimal('3.5')
    tracking.save()
    print("✅ Added 'stayed avg time' event")
    
    # Simulate "reopened" event
    tracking.reopened_once = True
    tracking.reopened_at = current_time
    tracking.view_count = 2
    tracking.save()
    print("✅ Added 'reopened' event")
    
    return tracking

def test_summary_file_creation(tracking):
    """Test that summary tracking files are created correctly"""
    print("\n📁 Testing Summary File Creation...")
    
    user = tracking.user
    document = tracking.document
    
    # Create summary tracking file
    summary_file = create_summary_tracking_file(document, user, tracking)
    
    if summary_file and os.path.exists(summary_file):
        print(f"✅ Summary tracking file created: {summary_file}")
        
        # Read and display file contents
        with open(summary_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"\n📄 File contents:\n{content}")
        
        # Verify file contains expected data
        checks = [
            ("✓ Reached end of the summary" in content, "Reached end event"),
            ("✓ Stayed ≥ average read time" in content, "Stayed avg time event"),
            ("✓ Reopened at least once" in content, "Reopened event"),
            ("View Count: 2" in content, "View count"),
            ("Predicted Average Time: 3.5" in content, "Predicted time")
        ]
        
        all_passed = True
        for check, description in checks:
            if check:
                print(f"✅ File contains {description}")
            else:
                print(f"❌ File missing {description}")
                all_passed = False
        
        return all_passed
    else:
        print("❌ Failed to create summary tracking file")
        return False

def test_no_duplicate_creation():
    """Test that no duplicate tracking records are created"""
    print("\n🔍 Testing No Duplicate Creation...")
    
    user = User.objects.get(username='summary_test_user')
    document = Document.objects.get(title='Summary Test Document', user=user)
    
    # Try to create another tracking record using the fixed logic
    tracking = SummaryTracking.objects.filter(
        document=document,
        user=user
    ).first()
    
    if not tracking:
        tracking = SummaryTracking.objects.create(
            document=document,
            user=user,
            first_view_time=timezone.now()
        )
        print("❌ Created duplicate tracking record")
        return False
    else:
        print("✅ Found existing tracking record (no duplicate created)")
    
    # Check total count
    total_count = SummaryTracking.objects.filter(
        document=document,
        user=user
    ).count()
    
    if total_count == 1:
        print(f"✅ Only 1 tracking record exists (expected)")
        return True
    else:
        print(f"❌ Found {total_count} tracking records (expected 1)")
        return False

def check_file_location():
    """Check that summary files are created in the correct location"""
    print("\n🔍 Checking Summary File Location...")
    
    user = User.objects.get(username='summary_test_user')
    expected_dir = f"core/media/summary_tracking/user_{user.id}"
    
    if os.path.exists(expected_dir):
        print(f"✅ Summary tracking directory exists: {expected_dir}")
        
        files = os.listdir(expected_dir)
        print(f"📁 Files in directory: {files}")
        
        if len(files) > 0:
            print("✅ Summary tracking files are being created")
            return True
        else:
            print("❌ No summary tracking files found")
            return False
    else:
        print(f"❌ Summary tracking directory not found: {expected_dir}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Fixed Summary Tracking Tests")
    print("=" * 50)
    
    try:
        # Test tracking creation
        tracking = test_summary_tracking_creation()
        
        # Test file creation
        test2_success = test_summary_file_creation(tracking)
        
        # Test no duplicates
        test3_success = test_no_duplicate_creation()
        
        # Check file location
        test4_success = check_file_location()
        
        print("\n" + "=" * 50)
        if test2_success and test3_success and test4_success:
            print("🎉 All summary tracking tests passed!")
            print("✅ Summary tracking creation is working correctly")
            print("✅ Summary files are created with correct data")
            print("✅ No duplicate records are created")
            print("✅ Files are created in correct location")
        else:
            print("❌ Some summary tracking tests failed")
            print(f"File creation: {'✅' if test2_success else '❌'}")
            print(f"No duplicates: {'✅' if test3_success else '❌'}")
            print(f"File location: {'✅' if test4_success else '❌'}")
        
        return test2_success and test3_success and test4_success
        
    except Exception as e:
        print(f"\n❌ Summary tracking tests failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
