#!/usr/bin/env python3
"""
Test all fixes for timer and summary tracking
"""

import os
import sys
import django
import time
import requests
from datetime import datetime

# Add the core directory to the path
core_path = os.path.join(os.path.dirname(__file__), 'core')
sys.path.insert(0, core_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from documents.models import Document, TimerSession, SummaryInteraction, SummaryTracking
from django.utils import timezone
import pytz

User = get_user_model()

def test_indian_timezone():
    """Test that Indian timezone is working"""
    print("🔄 Testing Indian Timezone...")
    
    # Get current time in Indian timezone
    indian_tz = pytz.timezone('Asia/Kolkata')
    current_time = timezone.now()
    indian_time = current_time.astimezone(indian_tz)
    
    print(f"✅ UTC Time: {current_time}")
    print(f"✅ Indian Time: {indian_time}")
    print(f"✅ Formatted: {indian_time.strftime('%Y-%m-%d %H:%M:%S IST')}")
    
    return True

def test_timer_database_storage():
    """Test timer storage in database"""
    print("\n🔄 Testing Timer Database Storage...")
    
    # Get or create test user
    user, created = User.objects.get_or_create(
        username='db_timer_test_user',
        defaults={'email': '<EMAIL>'}
    )
    print(f"✅ Using user: {user.username}")
    
    # Get or create test document
    document, created = Document.objects.get_or_create(
        title='DB Timer Test Document',
        user=user,
        defaults={'processing_status': 'completed'}
    )
    print(f"✅ Using document: {document.title}")
    
    # Clean up existing sessions
    TimerSession.objects.filter(document=document, user=user).delete()
    print("🧹 Cleaned up existing timer sessions")
    
    # Create timer session with Indian time
    indian_tz = pytz.timezone('Asia/Kolkata')
    start_time = timezone.now().astimezone(indian_tz)
    
    # Simulate 2 seconds
    time.sleep(2)
    
    end_time = timezone.now().astimezone(indian_tz)
    total_minutes = (end_time - start_time).total_seconds() / 60
    
    session = TimerSession.objects.create(
        document=document,
        user=user,
        session_number=1,
        start_time=start_time,
        end_time=end_time,
        total_minutes=total_minutes,
        ended_by_quiz=True
    )
    
    print(f"✅ Created timer session: {session.session_number}")
    print(f"📊 Start: {session.start_time}")
    print(f"📊 End: {session.end_time}")
    print(f"📊 Total: {session.total_minutes} minutes")
    
    return session

def test_summary_database_storage():
    """Test summary interaction storage in database"""
    print("\n🔄 Testing Summary Database Storage...")
    
    user = User.objects.get(username='db_timer_test_user')
    document = Document.objects.get(title='DB Timer Test Document', user=user)
    
    # Clean up existing interactions
    SummaryInteraction.objects.filter(document=document, user=user).delete()
    SummaryTracking.objects.filter(document=document, user=user).delete()
    print("🧹 Cleaned up existing summary data")
    
    # Create summary tracking
    tracking = SummaryTracking.objects.create(
        document=document,
        user=user,
        first_view_time=timezone.now(),
        view_count=1
    )
    
    # Create interactions with Indian time
    indian_tz = pytz.timezone('Asia/Kolkata')
    current_time = timezone.now().astimezone(indian_tz)
    
    interactions = [
        ('view_start', {}),
        ('reached_end', {}),
        ('stayed_avg_time', {'predicted_avg_time': 3.5}),
        ('reopened', {})
    ]
    
    for event_type, additional_data in interactions:
        interaction = SummaryInteraction.objects.create(
            document=document,
            user=user,
            event_type=event_type,
            event_time=current_time,
            additional_data=additional_data if additional_data else None
        )
        print(f"✅ Created interaction: {interaction.event_type}")
    
    return tracking

def test_api_endpoints():
    """Test the new API endpoints"""
    print("\n🔄 Testing API Endpoints...")
    
    user = User.objects.get(username='db_timer_test_user')
    document = Document.objects.get(title='DB Timer Test Document', user=user)
    
    # Test timer sessions endpoint
    timer_sessions = TimerSession.objects.filter(document=document, user=user)
    print(f"✅ Timer sessions in DB: {timer_sessions.count()}")
    
    # Test summary interactions endpoint
    summary_interactions = SummaryInteraction.objects.filter(document=document, user=user)
    print(f"✅ Summary interactions in DB: {summary_interactions.count()}")
    
    # Display data
    print("\n📊 Timer Sessions:")
    for session in timer_sessions:
        print(f"  Session {session.session_number}: {session.total_minutes} min")
        print(f"    Start: {session.start_time}")
        print(f"    End: {session.end_time}")
    
    print("\n📊 Summary Interactions:")
    for interaction in summary_interactions:
        print(f"  {interaction.event_type}: {interaction.event_time}")
        if interaction.additional_data:
            print(f"    Data: {interaction.additional_data}")
    
    return True

def test_view_count_logic():
    """Test the view count and reopening logic"""
    print("\n🔄 Testing View Count Logic...")
    
    user = User.objects.get(username='db_timer_test_user')
    document = Document.objects.get(title='DB Timer Test Document', user=user)
    
    # Clean up and start fresh
    SummaryTracking.objects.filter(document=document, user=user).delete()
    SummaryInteraction.objects.filter(document=document, user=user).delete()
    
    # Create tracking
    tracking = SummaryTracking.objects.create(
        document=document,
        user=user,
        first_view_time=timezone.now(),
        view_count=0  # Start from 0
    )
    
    # Simulate first view
    tracking.view_count = 1
    tracking.save()
    print(f"✅ First view: view_count = {tracking.view_count}, reopened = {tracking.reopened_once}")
    
    # Simulate second view (should trigger reopening)
    tracking.view_count = 2
    if tracking.view_count > 1 and not tracking.reopened_once:
        tracking.reopened_once = True
        tracking.reopened_at = timezone.now()
    tracking.save()
    print(f"✅ Second view: view_count = {tracking.view_count}, reopened = {tracking.reopened_once}")
    
    # Verify logic
    if tracking.view_count == 2 and tracking.reopened_once:
        print("✅ View count and reopening logic working correctly")
        return True
    else:
        print("❌ View count or reopening logic failed")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Comprehensive Fix Tests")
    print("=" * 60)
    
    try:
        # Test Indian timezone
        test1_success = test_indian_timezone()
        
        # Test timer database storage
        timer_session = test_timer_database_storage()
        
        # Test summary database storage
        summary_tracking = test_summary_database_storage()
        
        # Test API endpoints
        test4_success = test_api_endpoints()
        
        # Test view count logic
        test5_success = test_view_count_logic()
        
        print("\n" + "=" * 60)
        if test1_success and timer_session and summary_tracking and test4_success and test5_success:
            print("🎉 All fix tests passed!")
            print("✅ Indian timezone is working")
            print("✅ Timer database storage is working")
            print("✅ Summary database storage is working")
            print("✅ API endpoints are working")
            print("✅ View count logic is working")
            print("\n🎯 Key Improvements:")
            print("  • Timer data saved to database with Indian time")
            print("  • Summary interactions saved to database with Indian time")
            print("  • View count starts from 1 and reopening works correctly")
            print("  • API endpoints available to retrieve all tracking data")
            print("  • No more file-based storage - everything in database")
        else:
            print("❌ Some fix tests failed")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Fix tests failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
