#!/usr/bin/env python3
"""
Fix duplicate SummaryTracking records in the database
"""

import os
import sys
import django

# Add the core directory to the path
core_path = os.path.join(os.path.dirname(__file__), 'core')
sys.path.insert(0, core_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from documents.models import Document, SummaryTracking
from django.db.models import Count

User = get_user_model()

def fix_duplicate_tracking_records():
    """Remove duplicate SummaryTracking records, keeping the most recent one"""
    print("🔄 Fixing duplicate SummaryTracking records...")
    
    # Find documents with multiple tracking records for the same user
    duplicates = SummaryTracking.objects.values('document', 'user').annotate(
        count=Count('id')
    ).filter(count__gt=1)
    
    print(f"Found {len(duplicates)} document-user combinations with duplicate tracking records")
    
    fixed_count = 0
    for duplicate in duplicates:
        document_id = duplicate['document']
        user_id = duplicate['user']
        count = duplicate['count']
        
        print(f"\n📍 Fixing duplicates for Document {document_id}, User {user_id} ({count} records)")
        
        # Get all tracking records for this document-user combination
        tracking_records = SummaryTracking.objects.filter(
            document_id=document_id,
            user_id=user_id
        ).order_by('-summary_generated_at', '-id')  # Most recent first
        
        # Keep the first (most recent) record
        keep_record = tracking_records.first()
        
        # Merge data from all records into the one we're keeping
        for record in tracking_records:
            if record.id != keep_record.id:
                # Merge boolean flags (OR operation - if any record has True, keep True)
                if record.reached_end and not keep_record.reached_end:
                    keep_record.reached_end = True
                    keep_record.reached_end_at = record.reached_end_at
                
                if record.stayed_avg_time and not keep_record.stayed_avg_time:
                    keep_record.stayed_avg_time = True
                    keep_record.stayed_avg_time_at = record.stayed_avg_time_at
                
                if record.reopened_once and not keep_record.reopened_once:
                    keep_record.reopened_once = True
                    keep_record.reopened_at = record.reopened_at
                
                # Keep the earliest first_view_time
                if record.first_view_time and (not keep_record.first_view_time or record.first_view_time < keep_record.first_view_time):
                    keep_record.first_view_time = record.first_view_time
                
                # Sum up view counts
                keep_record.view_count += record.view_count
                
                # Keep the latest predicted time if available
                if record.predicted_avg_time_minutes and not keep_record.predicted_avg_time_minutes:
                    keep_record.predicted_avg_time_minutes = record.predicted_avg_time_minutes
                
                # Sum up total view time
                if record.total_view_time_minutes:
                    keep_record.total_view_time_minutes = (keep_record.total_view_time_minutes or 0) + record.total_view_time_minutes
        
        # Save the merged record
        keep_record.save()
        print(f"✅ Merged data into record {keep_record.id}")
        
        # Delete the duplicate records
        delete_records = tracking_records.exclude(id=keep_record.id)
        deleted_count = delete_records.count()
        delete_records.delete()
        print(f"🗑️  Deleted {deleted_count} duplicate records")
        
        fixed_count += 1
    
    print(f"\n🎉 Fixed {fixed_count} duplicate tracking record groups")
    return fixed_count

def verify_no_duplicates():
    """Verify that there are no more duplicate records"""
    print("\n🔍 Verifying no duplicate records remain...")
    
    duplicates = SummaryTracking.objects.values('document', 'user').annotate(
        count=Count('id')
    ).filter(count__gt=1)
    
    if len(duplicates) == 0:
        print("✅ No duplicate records found - database is clean!")
        return True
    else:
        print(f"❌ Still found {len(duplicates)} duplicate groups")
        return False

def show_tracking_summary():
    """Show summary of tracking records"""
    print("\n📊 Summary of SummaryTracking records:")
    
    total_records = SummaryTracking.objects.count()
    print(f"Total tracking records: {total_records}")
    
    records_with_reached_end = SummaryTracking.objects.filter(reached_end=True).count()
    print(f"Records with 'reached end': {records_with_reached_end}")
    
    records_with_stayed_avg = SummaryTracking.objects.filter(stayed_avg_time=True).count()
    print(f"Records with 'stayed avg time': {records_with_stayed_avg}")
    
    records_with_reopened = SummaryTracking.objects.filter(reopened_once=True).count()
    print(f"Records with 'reopened once': {records_with_reopened}")

def main():
    """Main function"""
    print("🚀 Starting SummaryTracking Duplicate Fix")
    print("=" * 50)
    
    try:
        # Show initial state
        show_tracking_summary()
        
        # Fix duplicates
        fixed_count = fix_duplicate_tracking_records()
        
        # Verify fix
        is_clean = verify_no_duplicates()
        
        # Show final state
        show_tracking_summary()
        
        print("\n" + "=" * 50)
        if is_clean:
            print("🎉 Database cleanup completed successfully!")
            print(f"✅ Fixed {fixed_count} duplicate groups")
            print("✅ No duplicate records remain")
        else:
            print("⚠️  Database cleanup completed with issues")
            print("❌ Some duplicates may still exist")
        
        return is_clean
        
    except Exception as e:
        print(f"\n❌ Cleanup failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
