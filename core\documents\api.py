from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
import logging
import os
import requests

from .models import Document, DocumentEmbedding, BlueprintTopics, DocumentTimer, SummaryTracking, TimerSession, SummaryInteraction
from .serializers import DocumentSerializer, DocumentEmbeddingSerializer, BlueprintTopicsSerializer
from django.utils import timezone
from decimal import Decimal
import json
import pytz

logger = logging.getLogger(__name__)

GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def store_embeddings(request, document_id):
    """
    Store document embeddings received from FastAPI server
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the chunks from the request
        chunks = request.data.get('chunks', [])

        if not chunks:
            return Response(
                {"error": "No chunks provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # First delete any existing embeddings for this document
        DocumentEmbedding.objects.filter(document=document).delete()

        # Create embedding objects for each chunk
        for chunk in chunks:
            DocumentEmbedding.objects.create(
                document=document,
                text_chunk=chunk['text'],
                embedding=chunk['embedding'],
                chunk_number=chunk['chunk_number']
            )

        # Update document status to completed
        document.processing_status = 'completed'
        document.error_message = None  # Clear any previous error
        document.save()

        return Response({
            "message": "Embeddings stored successfully",
            "document_id": document_id,
            "num_chunks": len(chunks)
        })

    except Exception as e:
        logger.error(f"Error storing embeddings: {str(e)}")
        return Response(
            {"error": f"Error storing embeddings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def store_topics(request, document_id):
    """
    Store blueprint topics received from FastAPI server
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the topics from the request
        topics = request.data.get('topics', [])

        if not topics:
            return Response(
                {"error": "No topics provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # First delete any existing topics for this document
        BlueprintTopics.objects.filter(document=document).delete()

        # Create topic objects for each topic
        created_topics = []
        for topic in topics:
            blueprint_topic = BlueprintTopics.objects.create(
                document=document,
                title=topic['title'],
                weightage=topic['weightage']
            )

            # Find relevant embeddings for this topic
            embeddings = DocumentEmbedding.objects.filter(document=document)

            if embeddings.exists():
                # Use semantic search to find relevant embeddings
                from sentence_transformers import SentenceTransformer
                import numpy as np

                # Initialize the sentence transformer model
                model = SentenceTransformer('all-MiniLM-L6-v2')

                # Generate embedding for the topic
                topic_embedding = model.encode([topic['title']])[0]

                # Calculate similarity scores
                similarities = []
                for idx, embedding_obj in enumerate(embeddings):
                    embedding_vector = np.array(embedding_obj.embedding)
                    similarity = np.dot(topic_embedding, embedding_vector) / (
                        np.linalg.norm(topic_embedding) * np.linalg.norm(embedding_vector)
                    )
                    similarities.append((similarity, idx, embedding_obj))

                # Sort by similarity (highest first)
                similarities.sort(reverse=True)

                # Get top 5 or 30% of embeddings, whichever is greater
                num_to_select = max(5, int(len(embeddings) * 0.3))
                relevant_embeddings = [emb for _, _, emb in similarities[:num_to_select]]

                # Add embeddings to the topic
                blueprint_topic.content.add(*relevant_embeddings)

            created_topics.append(blueprint_topic)

        # Update document status to completed
        document.processing_status = 'completed'
        document.error_message = None  # Clear any previous error
        document.save()

        return Response({
            "message": "Topics stored successfully",
            "document_id": document_id,
            "num_topics": len(created_topics)
        })

    except Exception as e:
        logger.error(f"Error storing topics: {str(e)}")
        return Response(
            {"error": f"Error storing topics: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_document_status(request, document_id):
    """
    Update document status
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the status from the request
        status_value = request.data.get('status')
        error_message = request.data.get('error_message')

        if not status_value:
            return Response(
                {"error": "No status provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update document status
        document.processing_status = status_value
        if error_message:
            document.error_message = error_message
        document.save()

        return Response({
            "message": "Document status updated successfully",
            "document_id": document_id,
            "status": status_value
        })

    except Exception as e:
        logger.error(f"Error updating document status: {str(e)}")
        return Response(
            {"error": f"Error updating document status: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def find_relevant_embeddings(request, document_id):
    """
    Find embeddings relevant to a topic
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the topic from the request
        topic = request.data.get('topic')

        if not topic:
            return Response(
                {"error": "No topic provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get all embeddings for this document
        embeddings = DocumentEmbedding.objects.filter(document=document)

        if not embeddings.exists():
            return Response(
                {"error": "Document has no embeddings"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Use semantic search to find relevant embeddings
        from sentence_transformers import SentenceTransformer
        import numpy as np

        # Initialize the sentence transformer model
        model = SentenceTransformer('all-MiniLM-L6-v2')

        # Generate embedding for the topic
        topic_embedding = model.encode([topic])[0]

        # Calculate similarity scores
        similarities = []
        for idx, embedding_obj in enumerate(embeddings):
            embedding_vector = np.array(embedding_obj.embedding)
            similarity = np.dot(topic_embedding, embedding_vector) / (
                np.linalg.norm(topic_embedding) * np.linalg.norm(embedding_vector)
            )
            similarities.append((similarity, idx, embedding_obj))

        # Sort by similarity (highest first)
        similarities.sort(reverse=True)

        # Get top 5 or 30% of embeddings, whichever is greater
        num_to_select = max(5, int(len(embeddings) * 0.3))
        relevant_embeddings = [emb for _, _, emb in similarities[:num_to_select]]

        return Response({
            "embedding_ids": [emb.id for emb in relevant_embeddings]
        })

    except Exception as e:
        logger.error(f"Error finding relevant embeddings: {str(e)}")
        return Response(
            {"error": f"Error finding relevant embeddings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_document_embeddings(request, document_id):
    """
    Get all embeddings for a document
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get all embeddings for this document
        embeddings = DocumentEmbedding.objects.filter(document=document).order_by('chunk_number')

        if not embeddings.exists():
            return Response(
                {"error": "Document has no embeddings"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Serialize the embeddings
        serializer = DocumentEmbeddingSerializer(embeddings, many=True)

        return Response(serializer.data)

    except Exception as e:
        logger.error(f"Error retrieving document embeddings: {str(e)}")
        return Response(
            {"error": f"Error retrieving document embeddings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def get_or_generate_flowchart(request, document_id):
    document = get_object_or_404(Document, id=document_id, user=request.user)

    if request.method == "GET":
        # Get existing flowchart for the document
        from .models import Flowchart
        try:
            flowchart = Flowchart.objects.get(document=document)
            return Response({
                "id": flowchart.id,
                "mermaid_code": flowchart.mermaid_code,
                "document": flowchart.document.id
            })
        except Flowchart.DoesNotExist:
            return Response([], status=200)  # Return empty array if no flowchart exists

    elif request.method == "POST":
        # Generate new flowchart
        # RAG: join all text chunks
        chunks = DocumentEmbedding.objects.filter(document=document).order_by('chunk_number')
        document_text = "\n\n".join(chunk.text_chunk for chunk in chunks)
        prompt = f"""
        You are an expert technical writer. Generate a flowchart using Mermaid syntax based on the following document content:

        "{document_text}"

        Only return valid Mermaid flowchart code. Do not add any explanations or text outside the Mermaid code.
        """
        try:
            response = requests.post(
                "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
                params={"key": GEMINI_API_KEY},
                json={
                    "contents": [
                        {"parts": [{"text": prompt}]}
                    ]
                },
                timeout=60
            )
            response.raise_for_status()
            gemini_output = response.json()
            generated_text = gemini_output.get("candidates", [])[0].get("content", {}).get("parts", [])[0].get("text", "")

            # Store the generated flowchart in the database
            from .models import Flowchart
            flowchart, created = Flowchart.objects.get_or_create(
                document=document,
                defaults={'mermaid_code': generated_text}
            )
            if not created:
                # Update existing flowchart
                flowchart.mermaid_code = generated_text
                flowchart.save()

            return Response({"flowchart": generated_text})
        except Exception as e:
            print(f"Error generating flowchart: {e}")
            return Response({"error": "Failed to generate flowchart."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def get_or_generate_flashcards(request, document_id):
    document = get_object_or_404(Document, id=document_id, user=request.user)

    if request.method == "GET":
        # Get existing flashcards for the document
        from .models import Flashcard
        flashcards = Flashcard.objects.filter(document=document)
        flashcard_data = [
            {
                "id": card.id,
                "front": card.front,
                "back": card.back,
                "document": card.document.id
            }
            for card in flashcards
        ]
        return Response(flashcard_data)

    elif request.method == "POST":
        # Generate new flashcards
        num_flashcards = int(request.data.get("num_flashcards", 10))
        chunks = DocumentEmbedding.objects.filter(document=document).order_by('chunk_number')
        document_text = "\n\n".join(chunk.text_chunk for chunk in chunks)
        prompt = f"""
        You are an expert tutor. Based on the following content, create {num_flashcards} flashcards.\n\nFormat:\nQuestion: ...\nAnswer: ...\n\nContent:\n{document_text}\n\nReturn only the flashcards, no extra text.
        """
        try:
            response = requests.post(
                "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
                params={"key": GEMINI_API_KEY},
                json={
                    "contents": [
                        {"parts": [{"text": prompt}]}
                    ]
                },
                timeout=60
            )
            response.raise_for_status()
            gemini_output = response.json()
            generated_text = gemini_output.get("candidates", [])[0].get("content", {}).get("parts", [])[0].get("text", "")
            # Parse Q/A pairs
            flashcards = []
            for qa in generated_text.strip().split("\n\n"):
                q = next((line for line in qa.split("\n") if line.strip().lower().startswith("question:")), None)
                a = next((line for line in qa.split("\n") if line.strip().lower().startswith("answer:")), None)
                if q and a:
                    flashcards.append({
                        "question": q.replace("Question:", "").strip(),
                        "answer": a.replace("Answer:", "").strip()
                    })
            return Response({"flashcards": flashcards})
        except Exception as e:
            print(f"Error generating flashcards: {e}")
            return Response({"error": "Failed to generate flashcards."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(["POST"])
@permission_classes([IsAuthenticated])
def generate_quiz(request, document_id):
    # document_id comes from URL parameter now
    num_questions = int(request.data.get("num_questions", 5))
    document = get_object_or_404(Document, id=document_id, user=request.user)
    chunks = DocumentEmbedding.objects.filter(document=document).order_by('chunk_number')
    document_text = "\n\n".join(chunk.text_chunk for chunk in chunks)
    prompt = f"""
    You are an educational quiz generator. Based on the following content, create {num_questions} quiz questions and their answers.\n\nFormat:\nQuestion: ...\nAnswer: ...\n\nContent:\n{document_text}\n\nReturn only the questions and answers, no extra text.
    """
    try:
        response = requests.post(
            "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
            params={"key": GEMINI_API_KEY},
            json={
                "contents": [
                    {"parts": [{"text": prompt}]}
                ]
            },
            timeout=60
        )
        response.raise_for_status()
        gemini_output = response.json()
        generated_text = gemini_output.get("candidates", [])[0].get("content", {}).get("parts", [])[0].get("text", "")
        # Parse Q/A pairs
        questions = []
        for qa in generated_text.strip().split("\n\n"):
            q = next((line for line in qa.split("\n") if line.strip().lower().startswith("question:")), None)
            a = next((line for line in qa.split("\n") if line.strip().lower().startswith("answer:")), None)
            if q and a:
                questions.append({
                    "question": q.replace("Question:", "").strip(),
                    "answer": a.replace("Answer:", "").strip()
                })
        return Response({"questions": questions})
    except Exception as e:
        print(f"Error generating quiz: {e}")
        return Response({"error": "Failed to generate quiz."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)




@api_view(["POST"])
@permission_classes([IsAuthenticated])
def process_blueprint_direct(request, document_id):
    """
    Direct endpoint for processing blueprints.
    Compatible with frontend API calls to /process-blueprint/{documentId}/
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get blueprint text and LLM model from request
        blueprint_text = request.data.get('blueprint_text')
        llm_model = request.data.get('llm_model', 'openai')

        # If no blueprint text provided, use the document's blueprint
        if not blueprint_text:
            if not document.blueprint:
                return Response(
                    {"error": "No blueprint text provided and document has no blueprint"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            blueprint_text = document.blueprint
        else:
            # Update document blueprint if new text provided
            document.blueprint = blueprint_text
            document.save()

        # Validate the LLM model
        if llm_model not in ['openai', 'gemini']:
            return Response(
                {"error": "Invalid LLM model. Use 'openai' or 'gemini'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the auth token
        auth_token = None
        if hasattr(request, 'auth') and request.auth:
            if hasattr(request.auth, 'token'):
                auth_token = request.auth.token
            elif hasattr(request.auth, 'key'):
                auth_token = request.auth.key
            else:
                auth_token = str(request.auth)

        if not auth_token:
            auth_token = f"user_{request.user.id}"

        # Set document status to processing
        document.processing_status = 'processing'
        document.save()

        # Import the direct processing function
        from .views import direct_process_blueprint

        # Try to use Celery task first, fall back to direct processing
        try:
            from .tasks import process_blueprint_task
            process_blueprint_task.delay(document.id, auth_token, llm_model)
            processing_method = "background task"
        except Exception as e:
            logger.warning(f"Celery task failed, falling back to direct processing: {str(e)}")
            # Fall back to direct processing
            result = direct_process_blueprint(document, auth_token, llm_model)
            processing_method = "direct request"

            # If there was an error, return it
            if 'error' in result:
                return Response({
                    "message": f"Blueprint processing failed: {result['error']}",
                    "document_id": document.id,
                    "status": "failed",
                    "error": result['error'],
                    "llm_model": llm_model
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({
            'message': f'Blueprint processing started via {processing_method} using {llm_model}.',
            'document_id': document.id,
            'status': 'processing',
            'llm_model': llm_model
        }, status=status.HTTP_202_ACCEPTED)

    except Exception as e:
        logger.error(f"Error processing blueprint for document {document_id}: {str(e)}")
        return Response(
            {"error": f"Error processing blueprint: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def get_or_generate_summary(request, document_id):
    """
    GET: Retrieve existing summary for a document
    POST: Generate new summary for a document
    """
    document = get_object_or_404(Document, id=document_id, user=request.user)

    if request.method == "GET":
        # Try to get existing summary from database
        try:
            from .models import Summary
            summary = Summary.objects.get(document=document)
            return Response({
                "summary": summary.content,
                "created_at": summary.created_at,
                "summary_type": getattr(summary, 'summary_type', 'comprehensive')
            })
        except ImportError:
            # Summary model doesn't exist yet
            return Response({"error": "No summary found for this document"}, status=status.HTTP_404_NOT_FOUND)
        except Exception:
            # Summary doesn't exist or other error
            return Response({"error": "No summary found for this document"}, status=status.HTTP_404_NOT_FOUND)

    elif request.method == "POST":
        # Generate new summary - this will be handled by FastAPI
        # For now, return a placeholder response
        return Response({
            "message": "Summary generation should be handled by FastAPI endpoint",
            "fastapi_endpoint": f"/generate-summary/{document_id}"
        }, status=status.HTTP_501_NOT_IMPLEMENTED)


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def get_or_generate_blueprint(request, document_id):
    """
    GET: Retrieve existing blueprint for a document
    POST: Generate new blueprint for a document
    """
    document = get_object_or_404(Document, id=document_id, user=request.user)

    if request.method == "GET":
        # Try to get existing blueprint from database
        try:
            from .models import Blueprint
            blueprint = Blueprint.objects.get(document=document)
            # Get associated topics
            topics = []
            if hasattr(blueprint, 'topics'):
                topics = [{"title": topic.title, "weightage": topic.weightage}
                         for topic in blueprint.topics.all()]

            return Response({
                "blueprint": blueprint.content,
                "topics": topics,
                "created_at": blueprint.created_at,
                "focus_areas": getattr(blueprint, 'focus_areas', '')
            })
        except Blueprint.DoesNotExist:
            return Response({"error": "No blueprint found for this document"}, status=status.HTTP_404_NOT_FOUND)
        except ImportError:
            # Blueprint model doesn't exist yet
            return Response({"error": "No blueprint found for this document"}, status=status.HTTP_404_NOT_FOUND)

    elif request.method == "POST":
        # Generate new blueprint - this will be handled by FastAPI
        # For now, return a placeholder response
        return Response({
            "message": "Blueprint generation should be handled by FastAPI endpoint",
            "fastapi_endpoint": f"/generate-blueprint/{document_id}"
        }, status=status.HTTP_501_NOT_IMPLEMENTED)


# Timer Tracking Endpoints

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_timer(request, document_id):
    """Start a new timer session for a document"""
    try:
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # End any existing active timer for this document and user
        DocumentTimer.objects.filter(
            document=document,
            user=request.user,
            is_active=True
        ).update(
            is_active=False,
            session_end_time=timezone.now()
        )

        # Create new timer session
        timer = DocumentTimer.objects.create(
            document=document,
            user=request.user,
            session_start_time=timezone.now(),
            is_active=True
        )

        return Response({
            "message": "Timer started successfully",
            "timer_id": timer.id,
            "start_time": timer.session_start_time.isoformat()
        })

    except Exception as e:
        logger.error(f"Error starting timer: {str(e)}")
        return Response(
            {"error": f"Error starting timer: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def pause_timer(request, document_id):
    """Pause the active timer for a document"""
    try:
        document = get_object_or_404(Document, id=document_id, user=request.user)

        timer = DocumentTimer.objects.filter(
            document=document,
            user=request.user,
            is_active=True
        ).first()

        if not timer:
            return Response(
                {"error": "No active timer found for this document"},
                status=status.HTTP_404_NOT_FOUND
            )

        timer.pause_time = timezone.now()
        timer.save()

        return Response({
            "message": "Timer paused successfully",
            "timer_id": timer.id,
            "pause_time": timer.pause_time.isoformat()
        })

    except Exception as e:
        logger.error(f"Error pausing timer: {str(e)}")
        return Response(
            {"error": f"Error pausing timer: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def resume_timer(request, document_id):
    """Resume a paused timer for a document"""
    try:
        document = get_object_or_404(Document, id=document_id, user=request.user)

        timer = DocumentTimer.objects.filter(
            document=document,
            user=request.user,
            is_active=True,
            pause_time__isnull=False
        ).first()

        if not timer:
            return Response(
                {"error": "No paused timer found for this document"},
                status=status.HTTP_404_NOT_FOUND
            )

        timer.resume_time = timezone.now()
        timer.pause_time = None  # Clear pause time
        timer.save()

        return Response({
            "message": "Timer resumed successfully",
            "timer_id": timer.id,
            "resume_time": timer.resume_time.isoformat()
        })

    except Exception as e:
        logger.error(f"Error resuming timer: {str(e)}")
        return Response(
            {"error": f"Error resuming timer: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def stop_timer(request, document_id):
    """Stop the active timer for a document (when quiz starts)"""
    try:
        document = get_object_or_404(Document, id=document_id, user=request.user)

        timer = DocumentTimer.objects.filter(
            document=document,
            user=request.user,
            is_active=True
        ).first()

        if not timer:
            return Response(
                {"error": "No active timer found for this document"},
                status=status.HTTP_404_NOT_FOUND
            )

        timer.session_end_time = timezone.now()
        timer.is_active = False
        timer.ended_by_quiz = True
        timer.save()

        # Save timer session to database with Indian time
        indian_start = timer.session_start_time.astimezone(pytz.timezone('Asia/Kolkata'))
        indian_end = timer.session_end_time.astimezone(pytz.timezone('Asia/Kolkata'))

        # Get session number
        session_count = TimerSession.objects.filter(document=document, user=request.user).count()

        TimerSession.objects.create(
            document=document,
            user=request.user,
            session_number=session_count + 1,
            start_time=indian_start,
            end_time=indian_end,
            pause_time=timer.pause_time.astimezone(pytz.timezone('Asia/Kolkata')) if timer.pause_time else None,
            resume_time=timer.resume_time.astimezone(pytz.timezone('Asia/Kolkata')) if timer.resume_time else None,
            total_minutes=timer.total_time_minutes,
            ended_by_quiz=True
        )

        return Response({
            "message": "Timer stopped successfully",
            "timer_id": timer.id,
            "end_time": timer.session_end_time.isoformat(),
            "total_time_minutes": timer.total_time_minutes
        })

    except Exception as e:
        logger.error(f"Error stopping timer: {str(e)}")
        return Response(
            {"error": f"Error stopping timer: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Summary Tracking Endpoints

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def track_summary_event(request, document_id):
    """Track summary interaction events"""
    try:
        document = get_object_or_404(Document, id=document_id, user=request.user)
        event_type = request.data.get('event_type')

        if not event_type:
            return Response(
                {"error": "event_type is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get or create summary tracking record (handle multiple records)
        try:
            tracking = SummaryTracking.objects.filter(
                document=document,
                user=request.user
            ).first()

            if not tracking:
                tracking = SummaryTracking.objects.create(
                    document=document,
                    user=request.user,
                    first_view_time=timezone.now()
                )
        except Exception as e:
            logger.error(f"Error getting/creating tracking record: {str(e)}")
            # If there are multiple records, get the first one
            tracking = SummaryTracking.objects.filter(
                document=document,
                user=request.user
            ).first()

        current_time = timezone.now()
        indian_time = current_time.astimezone(pytz.timezone('Asia/Kolkata'))

        # Save to database models
        additional_data = {}

        if event_type == 'reached_end':
            tracking.reached_end = True
            tracking.reached_end_at = current_time

        elif event_type == 'stayed_avg_time':
            tracking.stayed_avg_time = True
            tracking.stayed_avg_time_at = current_time
            additional_data['predicted_avg_time'] = float(tracking.predicted_avg_time_minutes) if tracking.predicted_avg_time_minutes else None

        elif event_type == 'reopened':
            tracking.reopened_once = True
            tracking.reopened_at = current_time
            # Don't increment view_count here, it should be done by view_start

        elif event_type == 'view_start':
            if not tracking.first_view_time:
                tracking.first_view_time = current_time
            tracking.view_count += 1
            # Check if this is a reopening (view_count > 1)
            if tracking.view_count > 1 and not tracking.reopened_once:
                tracking.reopened_once = True
                tracking.reopened_at = current_time

        tracking.save()

        # Save interaction to database with Indian time
        SummaryInteraction.objects.create(
            document=document,
            user=request.user,
            event_type=event_type,
            event_time=indian_time,
            additional_data=additional_data if additional_data else None
        )

        return Response({
            "message": f"Summary event '{event_type}' tracked successfully",
            "tracking_id": tracking.id
        })

    except Exception as e:
        logger.error(f"Error tracking summary event: {str(e)}")
        return Response(
            {"error": f"Error tracking summary event: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def calculate_avg_read_time(request, document_id):
    """Calculate average reading time for summary using Gemini"""
    try:
        document = get_object_or_404(Document, id=document_id, user=request.user)
        summary_text = request.data.get('summary_text')

        if not summary_text:
            return Response(
                {"error": "summary_text is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Use Gemini to predict average reading time
        prompt = f"""
        Calculate the average reading time in minutes for the following summary text.
        Consider an average reading speed of 200-250 words per minute.
        Return only a number representing minutes (can be decimal).

        Summary text:
        {summary_text}
        """

        response = requests.post(
            "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent",
            params={"key": GEMINI_API_KEY},
            json={
                "contents": [
                    {"parts": [{"text": prompt}]}
                ]
            },
            timeout=30
        )
        response.raise_for_status()
        gemini_output = response.json()
        predicted_time_str = gemini_output.get("candidates", [])[0].get("content", {}).get("parts", [])[0].get("text", "").strip()

        # Extract number from response
        import re
        time_match = re.search(r'(\d+\.?\d*)', predicted_time_str)
        predicted_time = float(time_match.group(1)) if time_match else 5.0  # Default to 5 minutes

        # Update summary tracking with predicted time (handle multiple records)
        try:
            tracking = SummaryTracking.objects.filter(
                document=document,
                user=request.user
            ).first()

            if not tracking:
                tracking = SummaryTracking.objects.create(
                    document=document,
                    user=request.user,
                    first_view_time=timezone.now()
                )
        except Exception as e:
            logger.error(f"Error getting/creating tracking record: {str(e)}")
            # If there are multiple records, get the first one
            tracking = SummaryTracking.objects.filter(
                document=document,
                user=request.user
            ).first()
        tracking.predicted_avg_time_minutes = Decimal(str(predicted_time))
        tracking.save()

        return Response({
            "predicted_avg_time_minutes": predicted_time,
            "tracking_id": tracking.id
        })

    except Exception as e:
        logger.error(f"Error calculating average read time: {str(e)}")
        return Response(
            {"error": f"Error calculating average read time: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Helper Functions

def get_indian_time():
    """Get current time in Indian timezone"""
    indian_tz = pytz.timezone('Asia/Kolkata')
    return timezone.now().astimezone(indian_tz)

def format_indian_time(dt):
    """Format datetime to Indian timezone string"""
    if dt:
        indian_tz = pytz.timezone('Asia/Kolkata')
        indian_time = dt.astimezone(indian_tz)
        return indian_time.strftime('%Y-%m-%d %H:%M:%S IST')
    return ""

# Helper Functions for File Creation

def create_timing_file(document, user):
    """Create timing file for document sessions"""
    try:
        from django.conf import settings

        # Create directory if it doesn't exist
        timing_dir = os.path.join(settings.MEDIA_ROOT, 'timing_files', f'user_{user.id}')
        os.makedirs(timing_dir, exist_ok=True)

        # Get all timer sessions for this document and user
        sessions = DocumentTimer.objects.filter(
            document=document,
            user=user
        ).order_by('created_at')

        # Create filename based on document
        safe_title = "".join(c for c in document.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"{safe_title}_{document.id}_timing.txt"
        filepath = os.path.join(timing_dir, filename)

        # Write timing data to file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"Timing Data for: {document.title}\n")
            f.write(f"Document ID: {document.id}\n")
            f.write(f"User: {user.username}\n")
            f.write(f"Generated: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 50 + "\n\n")

            total_time = Decimal('0')
            for i, session in enumerate(sessions, 1):
                f.write(f"Session {i}:\n")
                f.write(f"  Start Time: {session.session_start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                if session.session_end_time:
                    f.write(f"  End Time: {session.session_end_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"  Total Time: {session.total_time_minutes} minutes\n")
                    total_time += session.total_time_minutes
                else:
                    f.write(f"  Status: Active\n")

                if session.pause_time:
                    f.write(f"  Paused At: {session.pause_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                if session.resume_time:
                    f.write(f"  Resumed At: {session.resume_time.strftime('%Y-%m-%d %H:%M:%S')}\n")

                f.write(f"  Ended by Quiz: {'Yes' if session.ended_by_quiz else 'No'}\n")
                f.write("\n")

            f.write(f"Total Time Across All Sessions: {total_time} minutes\n")

        logger.info(f"Timing file created: {filepath}")
        return filepath

    except Exception as e:
        logger.error(f"Error creating timing file: {str(e)}")
        return None


def create_summary_tracking_file(document, user, tracking):
    """Create summary tracking file for document"""
    try:
        from django.conf import settings

        # Create directory if it doesn't exist
        tracking_dir = os.path.join(settings.MEDIA_ROOT, 'summary_tracking', f'user_{user.id}')
        os.makedirs(tracking_dir, exist_ok=True)

        # Create filename based on document
        safe_title = "".join(c for c in document.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"{safe_title}_{document.id}_summary_tracking.txt"
        filepath = os.path.join(tracking_dir, filename)

        # Write tracking data to file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"Summary Tracking for: {document.title}\n")
            f.write(f"Document ID: {document.id}\n")
            f.write(f"User: {user.username}\n")
            f.write(f"Generated: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 50 + "\n\n")

            # Write tracking events
            if tracking.reached_end:
                f.write("✓ Reached end of the summary\n")
                if tracking.reached_end_at:
                    f.write(f"  Time: {tracking.reached_end_at.strftime('%Y-%m-%d %H:%M:%S')}\n")

            if tracking.stayed_avg_time:
                f.write("✓ Stayed ≥ average read time\n")
                if tracking.stayed_avg_time_at:
                    f.write(f"  Time: {tracking.stayed_avg_time_at.strftime('%Y-%m-%d %H:%M:%S')}\n")
                if tracking.predicted_avg_time_minutes:
                    f.write(f"  Predicted Average Time: {tracking.predicted_avg_time_minutes} minutes\n")

            if tracking.reopened_once:
                f.write("✓ Reopened at least once\n")
                if tracking.reopened_at:
                    f.write(f"  Time: {tracking.reopened_at.strftime('%Y-%m-%d %H:%M:%S')}\n")

            f.write(f"\nView Count: {tracking.view_count}\n")
            if tracking.first_view_time:
                f.write(f"First View: {tracking.first_view_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            if tracking.total_view_time_minutes:
                f.write(f"Total View Time: {tracking.total_view_time_minutes} minutes\n")

        logger.info(f"Summary tracking file created: {filepath}")
        return filepath

    except Exception as e:
        logger.error(f"Error creating summary tracking file: {str(e)}")
        return None


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def test_file_creation(request, document_id):
    """Test endpoint to verify file creation functionality"""
    try:
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Test timing file creation
        timing_file = create_timing_file(document, request.user)

        # Test summary tracking file creation (create dummy tracking if none exists)
        tracking = SummaryTracking.objects.filter(
            document=document,
            user=request.user
        ).first()

        if not tracking:
            tracking = SummaryTracking.objects.create(
                document=document,
                user=request.user,
                first_view_time=timezone.now()
            )
        summary_file = create_summary_tracking_file(document, request.user, tracking)

        return Response({
            "message": "File creation test completed",
            "timing_file": timing_file,
            "summary_file": summary_file,
            "timing_file_exists": timing_file and os.path.exists(timing_file),
            "summary_file_exists": summary_file and os.path.exists(summary_file)
        })

    except Exception as e:
        logger.error(f"Error testing file creation: {str(e)}")
        return Response(
            {"error": f"Error testing file creation: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# New API Endpoints for Database Tracking

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_timer_sessions(request, document_id):
    """Get all timer sessions for a document"""
    try:
        document = get_object_or_404(Document, id=document_id, user=request.user)

        sessions = TimerSession.objects.filter(
            document=document,
            user=request.user
        ).order_by('session_number')

        sessions_data = []
        total_time = 0

        for session in sessions:
            session_data = {
                'session_number': session.session_number,
                'start_time': format_indian_time(session.start_time),
                'end_time': format_indian_time(session.end_time),
                'pause_time': format_indian_time(session.pause_time),
                'resume_time': format_indian_time(session.resume_time),
                'total_minutes': float(session.total_minutes),
                'ended_by_quiz': session.ended_by_quiz,
                'created_at': format_indian_time(session.created_at)
            }
            sessions_data.append(session_data)
            total_time += float(session.total_minutes)

        return Response({
            'document_title': document.title,
            'document_id': document.id,
            'user': request.user.username,
            'total_sessions': len(sessions_data),
            'total_time_minutes': total_time,
            'sessions': sessions_data
        })

    except Exception as e:
        logger.error(f"Error getting timer sessions: {str(e)}")
        return Response(
            {"error": f"Error getting timer sessions: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_summary_interactions(request, document_id):
    """Get all summary interactions for a document"""
    try:
        document = get_object_or_404(Document, id=document_id, user=request.user)

        interactions = SummaryInteraction.objects.filter(
            document=document,
            user=request.user
        ).order_by('created_at')

        # Get summary tracking data
        tracking = SummaryTracking.objects.filter(
            document=document,
            user=request.user
        ).first()

        interactions_data = []
        events_summary = {
            'reached_end': False,
            'stayed_avg_time': False,
            'reopened': False,
            'view_count': 0
        }

        for interaction in interactions:
            interaction_data = {
                'event_type': interaction.event_type,
                'event_time': format_indian_time(interaction.event_time),
                'additional_data': interaction.additional_data,
                'created_at': format_indian_time(interaction.created_at)
            }
            interactions_data.append(interaction_data)

            # Update events summary
            if interaction.event_type == 'reached_end':
                events_summary['reached_end'] = True
            elif interaction.event_type == 'stayed_avg_time':
                events_summary['stayed_avg_time'] = True
            elif interaction.event_type == 'reopened':
                events_summary['reopened'] = True
            elif interaction.event_type == 'view_start':
                events_summary['view_count'] += 1

        # Get tracking data if available
        tracking_data = {}
        if tracking:
            tracking_data = {
                'first_view_time': format_indian_time(tracking.first_view_time),
                'view_count': tracking.view_count,
                'predicted_avg_time_minutes': float(tracking.predicted_avg_time_minutes) if tracking.predicted_avg_time_minutes else None,
                'total_view_time_minutes': float(tracking.total_view_time_minutes) if tracking.total_view_time_minutes else None
            }

        return Response({
            'document_title': document.title,
            'document_id': document.id,
            'user': request.user.username,
            'events_summary': events_summary,
            'tracking_data': tracking_data,
            'total_interactions': len(interactions_data),
            'interactions': interactions_data
        })

    except Exception as e:
        logger.error(f"Error getting summary interactions: {str(e)}")
        return Response(
            {"error": f"Error getting summary interactions: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


