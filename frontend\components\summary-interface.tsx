"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Copy, CheckCircle, FileText, Sparkles, RefreshCw } from "lucide-react"
import { toast } from "sonner"
import { summaryApi, summaryTrackingApi } from "@/lib/api"
import { Markdown } from "@/components/ui/markdown"

interface SummaryInterfaceProps {
  documentId?: number
}

export function SummaryInterface({ documentId }: SummaryInterfaceProps) {
  const [summary, setSummary] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const [summaryType, setSummaryType] = useState<string>("comprehensive")
  const [isGenerating, setIsGenerating] = useState(false)

  // Summary tracking state
  const [viewStartTime, setViewStartTime] = useState<number | null>(null)
  const [hasReachedEnd, setHasReachedEnd] = useState(false)
  const [hasStayedAvgTime, setHasStayedAvgTime] = useState(false)
  const [hasReopened, setHasReopened] = useState(false)
  const [viewCount, setViewCount] = useState(0)
  const [predictedAvgTime, setPredictedAvgTime] = useState<number | null>(null)

  // Summary tracking functions
  const trackSummaryEvent = async (eventType: string) => {
    if (!documentId) return

    try {
      await summaryTrackingApi.trackEvent(documentId, eventType)
      console.log(`Summary event tracked: ${eventType}`)
    } catch (error) {
      console.error('Error tracking summary event:', error)
    }
  }

  const calculateAndSetAvgTime = async (summaryText: string) => {
    if (!documentId || !summaryText) return

    try {
      const response = await summaryTrackingApi.calculateAvgReadTime(documentId, summaryText)
      setPredictedAvgTime(response.predicted_avg_time_minutes)
    } catch (error) {
      console.error('Error calculating average read time:', error)
    }
  }

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget
    const scrollTop = element.scrollTop
    const scrollHeight = element.scrollHeight
    const clientHeight = element.clientHeight

    // Check if user has scrolled to the bottom (within 10px tolerance)
    if (scrollTop + clientHeight >= scrollHeight - 10 && !hasReachedEnd) {
      setHasReachedEnd(true)
      trackSummaryEvent('reached_end')
    }
  }

  // Track view time and check against average
  useEffect(() => {
    if (summary && viewStartTime && predictedAvgTime) {
      const checkAvgTime = () => {
        const currentTime = Date.now()
        const timeSpentMinutes = (currentTime - viewStartTime) / (1000 * 60)

        if (timeSpentMinutes >= predictedAvgTime && !hasStayedAvgTime) {
          setHasStayedAvgTime(true)
          trackSummaryEvent('stayed_avg_time')
        }
      }

      const interval = setInterval(checkAvgTime, 5000) // Check every 5 seconds
      return () => clearInterval(interval)
    }
  }, [summary, viewStartTime, predictedAvgTime, hasStayedAvgTime])

  useEffect(() => {
    if (documentId) {
      // Track reopening if this is not the first view
      if (viewCount > 0 && !hasReopened) {
        setHasReopened(true)
        trackSummaryEvent('reopened')
      }

      setViewCount(prev => prev + 1)
      loadSummary()
    }
  }, [documentId])

  // Separate effect to track reopening when user clicks reload or generate new
  const handleReload = () => {
    if (!hasReopened) {
      setHasReopened(true)
      trackSummaryEvent('reopened')
    }
    loadSummary()
  }

  const handleGenerateNew = () => {
    if (!hasReopened) {
      setHasReopened(true)
      trackSummaryEvent('reopened')
    }
    generateNewSummary()
  }

  const loadSummary = async () => {
    if (!documentId) {
      setError("No document selected")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // First try to get existing summary
      try {
        const existingSummary = await summaryApi.getSummary(documentId)
        if (existingSummary && existingSummary.summary) {
          setSummary(existingSummary.summary)
          // Calculate average read time and start tracking for existing summary
          calculateAndSetAvgTime(existingSummary.summary)
          setViewStartTime(Date.now())
          trackSummaryEvent('view_start')
          return
        }
      } catch (existingError) {
        console.log('No existing summary found, will generate new one')
      }

      // If no existing summary, generate new one
      console.log('Generating new summary for document:', documentId)
      const response = await summaryApi.generateSummary(documentId, summaryType)
      setSummary(response.summary)

      // Calculate average read time and start tracking
      if (response.summary) {
        calculateAndSetAvgTime(response.summary)
        setViewStartTime(Date.now())
        trackSummaryEvent('view_start')
      }
    } catch (err) {
      console.error('Error loading summary:', err)
      setError('Failed to load summary. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const generateNewSummary = async () => {
    if (!documentId) {
      toast.error("No document selected for summary generation")
      return
    }

    setIsGenerating(true)
    setError(null)

    try {
      console.log('Generating new summary for document:', documentId, 'type:', summaryType)
      const response = await summaryApi.generateSummary(documentId, summaryType)
      setSummary(response.summary)
      toast.success("New summary generated successfully!")
    } catch (err) {
      console.error('Error generating summary:', err)
      setError('Failed to generate new summary. Please try again.')
      toast.error("Failed to generate new summary")
    } finally {
      setIsGenerating(false)
    }
  }

  const copyToClipboard = async () => {
    if (!summary) return

    try {
      await navigator.clipboard.writeText(summary)
      setCopied(true)
      toast.success("Summary copied to clipboard!")
      
      // Reset copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
      toast.error("Failed to copy summary")
    }
  }

  if (!documentId) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Document Summary
            </CardTitle>
            <CardDescription>
              Select a document to view or generate its summary
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Please select a document to generate a summary.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Document Summary
            </CardTitle>
            <CardDescription>
              {summary ? "Generating new summary..." : "Loading summary..."}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Document Summary
            </CardTitle>
            <CardDescription className="text-red-500">
              {error}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={loadSummary} className="w-full">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Summary
          </CardTitle>
          <CardDescription>
            AI-generated summary of your document content
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Summary Type Selector */}
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Summary Type:</label>
            <Select value={summaryType} onValueChange={setSummaryType}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="comprehensive">Comprehensive</SelectItem>
                <SelectItem value="brief">Brief</SelectItem>
                <SelectItem value="key_points">Key Points</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Summary Content */}
          <div
            className="border rounded-lg p-4 bg-neutral-900/50 max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-neutral-500 scrollbar-track-transparent hover:scrollbar-thumb-neutral-400"
            onScroll={handleScroll}
          >
            <Markdown content={summary} />
          </div>

          <div className="flex gap-2 pt-4 border-t">
            <Button
              onClick={copyToClipboard}
              variant="outline"
              className="flex-1"
              disabled={!summary}
            >
              {copied ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Summary
                </>
              )}
            </Button>
            <Button
              onClick={handleReload}
              variant="outline"
              className="flex-1"
              disabled={isLoading || isGenerating}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Reload
            </Button>
            <Button
              onClick={handleGenerateNew}
              className="flex-1"
              disabled={isLoading || isGenerating}
            >
              <Sparkles className="h-4 w-4 mr-2" />
              {isGenerating ? "Generating..." : "Generate New"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
