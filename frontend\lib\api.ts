import axios from 'axios';

// Base URL for API requests
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Auth API endpoints
export const authApi = {
  signIn: async (credentials: { username: string; password: string }) => {
    try {
      const response = await api.post('/users/login/', credentials);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  register: async (userData: {
    username: string;
    email: string;
    password: string;
    confirm_password: string;
    first_name: string;
    last_name: string;
  }) => {
    try {
      const response = await api.post('/users/register/', userData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  verifyOTP: async (data: { email: string; otp: string }) => {
    try {
      const response = await api.post('/users/verify-otp/', data);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  logout: async () => {
    try {
      const response = await api.post('/users/logout/');
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Document API endpoints
export const documentApi = {
  uploadDocument: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await api.post('/documents/upload/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getDocuments: async () => {
    try {
      const response = await api.get('/documents/');
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getDocumentStatus: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getDocumentDetails: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getDocumentFileUrl: (documentId: number) => {
    // Construct the file URL for preview with proper authentication
    return `${API_BASE_URL}/documents/${documentId}/file/`;
  },

  getDocumentFileBlob: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/file/`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Chat API endpoints
export const chatApi = {
  sendMessage: async (message: string, documentId?: string, model: string = 'openai') => {
    try {
      const response = await api.post('/chat/message/', {
        message,
        document_id: documentId,
        model,
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getHistory: async () => {
    try {
      const response = await api.get('/chat/history/');
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};


// Performance API endpoints
export const performanceApi = {
  createPerformance: async (performanceData: {
    student: number;
    document: number;
    quiz_score: number;
    time_taken: number;
    remarks?: string;
  }) => {
    try {
      const response = await api.post('/users/performance/', performanceData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getPerformances: async (documentId?: number) => {
    try {
      const url = documentId
        ? `/users/performance/?document=${documentId}`
        : '/users/performance/';
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getStudentPerformances: async (studentId: number) => {
    try {
      const response = await api.get(`/users/performance/?student=${studentId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getPerformanceStats: async (documentId: number) => {
    try {
      const response = await api.get(`/users/performance/?document=${documentId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

}
// Blueprint API
export const blueprintApi = {
  // Get existing blueprint
  getBlueprint: async (documentId: number) => {
    try {
      const response = await api.get(`/blueprints/${documentId}/`);
      return response.data;
    } catch (error) {
      console.error('Error getting blueprint:', error);
      throw error;
    }
  },

  // Generate new blueprint using FastAPI
  generateBlueprint: async (
    documentId: number,
    focusAreas?: string,
    llmModel: string = 'gemini'
  ) => {
    try {
      const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';
      const token = localStorage.getItem('token');

      const response = await axios.post(
        `${fastApiUrl}/generate-blueprint/${documentId}`,
        {
          document_id: documentId,
          llm_model: llmModel,
          focus_areas: focusAreas,
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error generating blueprint:', error);
      throw error;
    }
  }
};

// Legacy blueprint function for backward compatibility
export const processBlueprint = async (
  documentId: number,
  blueprintText: string,
  llmModel: string = 'openai' // can be 'gemini', 'rag', 'openai', etc.
) => {
  try {
    const response = await api.post(`/process-blueprint/${documentId}/`, {
      document_id: documentId,
      blueprint_text: blueprintText,
      llm_model: llmModel,
    });
    return response.data;
  } catch (error) {
    console.error('Error processing blueprint:', error);
    throw error;
  }
};

// Flashcards API
export const flashcardsApi = {
  // Combined GET/POST endpoint - preferred method
  getFlashcards: async (documentId: number) => {
    try {
      const response = await api.get(`/flashcards/${documentId}/`);
      return response.data;
    } catch (error) {
      console.error('Error getting flashcards:', error);
      throw error;
    }
  },

  generateFlashcards: async (
    documentId: number,
    numFlashcards: number = 10,
    llmModel: string = 'gemini'
  ) => {
    try {
      const response = await api.post(`/flashcards/${documentId}/`, {
        num_flashcards: numFlashcards,
        llm_model: llmModel,
      });
      return response.data;
    } catch (error) {
      console.error('Error generating flashcards:', error);
      throw error;
    }
  },

  // Generate flashcards using FastAPI (enhanced version)
  generateFlashcardsEnhanced: async (
    documentId: number,
    numFlashcards: number = 10,
    llmModel: string = 'gemini'
  ) => {
    try {
      const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';
      const token = localStorage.getItem('token');

      const response = await axios.post(
        `${fastApiUrl}/generate-flashcards/${documentId}`,
        {
          document_id: documentId,
          llm_model: llmModel,
          num_flashcards: numFlashcards,
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error generating enhanced flashcards:', error);
      throw error;
    }
  }
};

// Flowchart API
export const flowchartApi = {
  // Combined GET/POST endpoint - preferred method
  getFlowchart: async (documentId: number) => {
    try {
      const response = await api.get(`/flowchart/${documentId}/`);
      return response.data;
    } catch (error) {
      console.error('Error getting flowchart:', error);
      throw error;
    }
  },

  generateFlowchart: async (
    documentId: number,
    llmModel: string = 'gemini'
  ) => {
    try {
      const response = await api.post(`/flowchart/${documentId}/`, {
        llm_model: llmModel,
      });
      return response.data;
    } catch (error) {
      console.error('Error generating flowchart:', error);
      throw error;
    }
  },

  // Generate flowchart using FastAPI (enhanced version)
  generateFlowchartEnhanced: async (
    documentId: number,
    llmModel: string = 'gemini'
  ) => {
    try {
      const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';
      const token = localStorage.getItem('token');

      const response = await axios.post(
        `${fastApiUrl}/generate-flowchart/${documentId}`,
        {
          document_id: documentId,
          llm_model: llmModel,
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error generating enhanced flowchart:', error);
      throw error;
    }
  }
};

// Summary API
export const summaryApi = {
  // Get existing summary
  getSummary: async (documentId: number) => {
    try {
      const response = await api.get(`/summaries/${documentId}/`);
      return response.data;
    } catch (error) {
      console.error('Error getting summary:', error);
      throw error;
    }
  },

  // Generate new summary using FastAPI
  generateSummary: async (
    documentId: number,
    summaryType: string = 'comprehensive',
    llmModel: string = 'gemini'
  ) => {
    try {
      const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';
      const token = localStorage.getItem('token');

      const response = await axios.post(
        `${fastApiUrl}/generate-summary/${documentId}`,
        {
          document_id: documentId,
          llm_model: llmModel,
          summary_type: summaryType,
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error generating summary:', error);
      throw error;
    }
  }
};

// Quiz API
export const quizApi = {
  // Combined GET/POST endpoint - preferred method
  getOrGenerateQuiz: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/quiz/`);
      return response.data;
    } catch (error) {
      console.error('Error getting/generating quiz:', error);
      throw error;
    }
  },

  generateQuiz: async (
    documentId: number,
    numQuestions: number = 5,
    llmModel: string = 'gemini'
  ) => {
    try {
      const response = await api.post(`/documents/${documentId}/quiz/`, {
        num_questions: numQuestions,
        llm_model: llmModel,
      });
      return response.data;
    } catch (error) {
      console.error('Error generating quiz:', error);
      throw error;
    }
  },

  // Generate quiz using FastAPI (enhanced version)
  generateQuizEnhanced: async (
    documentId: number,
    numQuestions: number = 5,
    llmModel: string = 'gemini'
  ) => {
    try {
      const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';
      const token = localStorage.getItem('token');

      const response = await axios.post(
        `${fastApiUrl}/generate-quiz/${documentId}`,
        {
          document_id: documentId,
          llm_model: llmModel,
          num_questions: numQuestions,
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error generating enhanced quiz:', error);
      throw error;
    }
  },

  // Legacy method for backward compatibility
  getQuizzes: async (documentId: number) => {
    try {
      const response = await api.get(`/quizzes/?document=${documentId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching quizzes:', error);
      throw error;
    }
  },

  submitQuizAnswer: async (quizId: number, answers: any) => {
    try {
      const response = await api.post(`/quizzes/${quizId}/submit/`, { answers });
      return response.data;
    } catch (error) {
      console.error('Error submitting quiz:', error);
      throw error;
    }
  }
};

// Timer API
export const timerApi = {
  startTimer: async (documentId: number) => {
    try {
      const response = await api.post(`/documents/${documentId}/timer/start/`);
      return response.data;
    } catch (error) {
      console.error('Error starting timer:', error);
      throw error;
    }
  },

  pauseTimer: async (documentId: number) => {
    try {
      const response = await api.post(`/documents/${documentId}/timer/pause/`);
      return response.data;
    } catch (error) {
      console.error('Error pausing timer:', error);
      throw error;
    }
  },

  resumeTimer: async (documentId: number) => {
    try {
      const response = await api.post(`/documents/${documentId}/timer/resume/`);
      return response.data;
    } catch (error) {
      console.error('Error resuming timer:', error);
      throw error;
    }
  },

  stopTimer: async (documentId: number) => {
    try {
      const response = await api.post(`/documents/${documentId}/timer/stop/`);
      return response.data;
    } catch (error) {
      console.error('Error stopping timer:', error);
      throw error;
    }
  }
};

// Summary Tracking API
export const summaryTrackingApi = {
  trackEvent: async (documentId: number, eventType: string) => {
    try {
      const response = await api.post(`/documents/${documentId}/summary/track/`, {
        event_type: eventType
      });
      return response.data;
    } catch (error) {
      console.error('Error tracking summary event:', error);
      throw error;
    }
  },

  calculateAvgReadTime: async (documentId: number, summaryText: string) => {
    try {
      const response = await api.post(`/documents/${documentId}/summary/avg-time/`, {
        summary_text: summaryText
      });
      return response.data;
    } catch (error) {
      console.error('Error calculating average read time:', error);
      throw error;
    }
  }
};

// Database Tracking API
export const trackingApi = {
  getTimerSessions: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/timer-sessions/`);
      return response.data;
    } catch (error) {
      console.error('Error getting timer sessions:', error);
      throw error;
    }
  },

  getSummaryInteractions: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/summary-interactions/`);
      return response.data;
    } catch (error) {
      console.error('Error getting summary interactions:', error);
      throw error;
    }
  }
};

// Legacy exports for backward compatibility
export const generateQuiz = quizApi.generateQuiz;
export const generateFlowchart = flowchartApi.generateFlowchart;
export const generateFlashcards = flashcardsApi.generateFlashcards;

export default api;
