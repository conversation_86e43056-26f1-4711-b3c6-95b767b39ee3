# Generated by Django 4.2.21 on 2025-06-23 14:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('documents', '0013_auto_20250619_0950'),
    ]

    operations = [
        migrations.CreateModel(
            name='TimerSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_number', models.IntegerField(help_text='Session number for this document')),
                ('start_time', models.DateTimeField(help_text='Session start time in Indian timezone')),
                ('end_time', models.DateTimeField(blank=True, help_text='Session end time in Indian timezone', null=True)),
                ('pause_time', models.DateTimeField(blank=True, help_text='When session was paused', null=True)),
                ('resume_time', models.DateTimeField(blank=True, help_text='When session was resumed', null=True)),
                ('total_minutes', models.DecimalField(decimal_places=2, default=0, help_text='Total time in minutes', max_digits=10)),
                ('ended_by_quiz', models.Boolean<PERSON>ield(default=False, help_text='Whether session ended by starting quiz')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='timer_sessions_db', to='documents.document')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='timer_sessions_db', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['document', 'user'], name='documents_t_documen_019ea6_idx')],
            },
        ),
        migrations.CreateModel(
            name='SummaryInteraction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(help_text='Type of interaction event', max_length=50)),
                ('event_time', models.DateTimeField(help_text='When the event occurred in Indian timezone')),
                ('additional_data', models.JSONField(blank=True, help_text='Additional event data', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='summary_interactions', to='documents.document')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='summary_interactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['document', 'user', 'event_type'], name='documents_s_documen_8e44e1_idx')],
            },
        ),
    ]
