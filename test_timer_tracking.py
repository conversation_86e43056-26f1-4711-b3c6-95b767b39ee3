#!/usr/bin/env python3
"""
Test script to verify timer and summary tracking functionality
"""

import os
import sys
import django
from datetime import datetime

# Add the core directory to the path
core_path = os.path.join(os.path.dirname(__file__), 'core')
sys.path.insert(0, core_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from documents.models import Document, DocumentTimer, SummaryTracking
from django.utils import timezone

User = get_user_model()

def test_timer_functionality():
    """Test the timer tracking functionality"""
    print("🔄 Testing Timer Functionality...")
    
    # Get or create a test user
    user, created = User.objects.get_or_create(
        username='test_user',
        defaults={'email': '<EMAIL>'}
    )
    if created:
        print(f"✅ Created test user: {user.username}")
    else:
        print(f"✅ Using existing test user: {user.username}")
    
    # Get or create a test document
    document, created = Document.objects.get_or_create(
        title='Test Document for Timer',
        user=user,
        defaults={'processing_status': 'completed'}
    )
    if created:
        print(f"✅ Created test document: {document.title}")
    else:
        print(f"✅ Using existing test document: {document.title}")
    
    # Test timer creation
    timer = DocumentTimer.objects.create(
        document=document,
        user=user,
        session_start_time=timezone.now(),
        is_active=True
    )
    print(f"✅ Created timer session: {timer.id}")
    
    # Test timer completion
    timer.session_end_time = timezone.now()
    timer.is_active = False
    timer.ended_by_quiz = True
    timer.save()
    print(f"✅ Timer session completed: {timer.total_time_minutes} minutes")
    
    return document, user

def test_summary_tracking_functionality(document, user):
    """Test the summary tracking functionality"""
    print("\n🔄 Testing Summary Tracking Functionality...")
    
    # Create summary tracking record
    tracking = SummaryTracking.objects.create(
        document=document,
        user=user,
        first_view_time=timezone.now(),
        reached_end=True,
        reached_end_at=timezone.now(),
        stayed_avg_time=True,
        stayed_avg_time_at=timezone.now(),
        reopened_once=True,
        reopened_at=timezone.now(),
        view_count=2,
        predicted_avg_time_minutes=5.5
    )
    print(f"✅ Created summary tracking: {tracking.id}")
    
    return tracking

def test_file_creation(document, user, tracking):
    """Test file creation functionality"""
    print("\n🔄 Testing File Creation...")
    
    # Import the file creation functions
    from documents.api import create_timing_file, create_summary_tracking_file
    
    # Test timing file creation
    timing_file = create_timing_file(document, user)
    if timing_file and os.path.exists(timing_file):
        print(f"✅ Timing file created: {timing_file}")
        with open(timing_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"📄 Timing file content preview:\n{content[:200]}...")
    else:
        print("❌ Failed to create timing file")
    
    # Test summary tracking file creation
    summary_file = create_summary_tracking_file(document, user, tracking)
    if summary_file and os.path.exists(summary_file):
        print(f"✅ Summary tracking file created: {summary_file}")
        with open(summary_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"📄 Summary tracking file content preview:\n{content[:200]}...")
    else:
        print("❌ Failed to create summary tracking file")

def test_model_relationships():
    """Test model relationships and queries"""
    print("\n🔄 Testing Model Relationships...")
    
    # Test timer queries
    timer_count = DocumentTimer.objects.count()
    print(f"✅ Total timer sessions: {timer_count}")
    
    # Test summary tracking queries
    tracking_count = SummaryTracking.objects.count()
    print(f"✅ Total summary tracking records: {tracking_count}")
    
    # Test active timers
    active_timers = DocumentTimer.objects.filter(is_active=True).count()
    print(f"✅ Active timer sessions: {active_timers}")
    
    # Test completed timers
    completed_timers = DocumentTimer.objects.filter(ended_by_quiz=True).count()
    print(f"✅ Quiz-completed timer sessions: {completed_timers}")

def main():
    """Main test function"""
    print("🚀 Starting Timer and Summary Tracking Tests")
    print("=" * 50)
    
    try:
        # Test timer functionality
        document, user = test_timer_functionality()
        
        # Test summary tracking functionality
        tracking = test_summary_tracking_functionality(document, user)
        
        # Test file creation
        test_file_creation(document, user, tracking)
        
        # Test model relationships
        test_model_relationships()
        
        print("\n" + "=" * 50)
        print("🎉 All tests completed successfully!")
        print("✅ Timer tracking functionality is working")
        print("✅ Summary tracking functionality is working")
        print("✅ File creation functionality is working")
        print("✅ Model relationships are working")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
