from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    DocumentViewSet, DocumentEmbeddingViewSet, FlashcardViewSet,
    FlowchartViewSet, QuizViewSet, BlueprintTopicsViewSet
)
from .api import (
    store_embeddings, store_topics, update_document_status,
    find_relevant_embeddings, get_document_embeddings,
    get_or_generate_summary, get_or_generate_blueprint,
    start_timer, pause_timer, resume_timer, stop_timer,
    track_summary_event, calculate_avg_read_time, test_file_creation
)

router = DefaultRouter()
router.register(r'', DocumentViewSet, basename='document')
router.register(r'embeddings', DocumentEmbeddingViewSet, basename='document-embedding')
router.register(r'flashcards', FlashcardViewSet, basename='flashcard')
router.register(r'flowcharts', FlowchartViewSet, basename='flowchart')
router.register(r'qa', QuizViewSet, basename='quiz')
router.register(r'topics', BlueprintTopicsViewSet, basename='blueprint-topics')

urlpatterns = [
    # Original routes
    path('', include(router.urls)),

    # Quiz endpoint - redirect to QuizViewSet
    path('<int:document_id>/quiz/', QuizViewSet.as_view({'get': 'document_quiz', 'post': 'document_quiz'}), name='document-quiz'),

    # API endpoints for FastAPI integration
    path('<int:document_id>/store-embeddings/', store_embeddings, name='store-embeddings'),
    path('<int:document_id>/store-topics/', store_topics, name='store-topics'),
    path('<int:document_id>/update-status/', update_document_status, name='update-document-status'),
    path('<int:document_id>/find-relevant-embeddings/', find_relevant_embeddings, name='find-relevant-embeddings'),
    path('<int:document_id>/embeddings/', get_document_embeddings, name='get-document-embeddings'),

    # Summary and Blueprint endpoints
    path('summaries/<int:document_id>/', get_or_generate_summary, name='get-or-generate-summary'),
    path('blueprints/<int:document_id>/', get_or_generate_blueprint, name='get-or-generate-blueprint'),

    # Timer endpoints
    path('<int:document_id>/timer/start/', start_timer, name='start-timer'),
    path('<int:document_id>/timer/pause/', pause_timer, name='pause-timer'),
    path('<int:document_id>/timer/resume/', resume_timer, name='resume-timer'),
    path('<int:document_id>/timer/stop/', stop_timer, name='stop-timer'),

    # Summary tracking endpoints
    path('<int:document_id>/summary/track/', track_summary_event, name='track-summary-event'),
    path('<int:document_id>/summary/avg-time/', calculate_avg_read_time, name='calculate-avg-read-time'),

    # Test endpoint
    path('<int:document_id>/test-files/', test_file_creation, name='test-file-creation'),
]

'''
GET /documents/ - List all documents for the current user
POST /documents/ - Create a new document (using the standard create method)
GET /documents/{id}/ - Retrieve a specific document
PUT /documents/{id}/ - Update a document
PATCH /documents/{id}/ - Partially update a document
DELETE /documents/{id}/ - Delete a document
GET /documents/{id}/processing_status/ - Get document processing status (custom action)
POST /documents/upload/ - Upload a document file (custom action)
GET /documents/{id}/blueprint/ - Get document blueprint (custom action)
POST /documents/{id}/blueprint/ - Upload document blueprint (custom action)
'''