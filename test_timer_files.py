#!/usr/bin/env python3
"""
Test timer file creation functionality
"""

import os
import sys
import django
import time

# Add the core directory to the path
core_path = os.path.join(os.path.dirname(__file__), 'core')
sys.path.insert(0, core_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from documents.models import Document, DocumentTimer
from django.utils import timezone
from documents.api import create_timing_file

User = get_user_model()

def test_timer_file_creation():
    """Test that timer files are created correctly"""
    print("🔄 Testing Timer File Creation...")
    
    # Get or create test user
    user, created = User.objects.get_or_create(
        username='timer_file_test_user',
        defaults={'email': '<EMAIL>'}
    )
    print(f"✅ Using user: {user.username}")
    
    # Get or create test document
    document, created = Document.objects.get_or_create(
        title='Timer File Test Document',
        user=user,
        defaults={'processing_status': 'completed'}
    )
    print(f"✅ Using document: {document.title}")
    
    # Clean up existing timers
    DocumentTimer.objects.filter(document=document, user=user).delete()
    print("🧹 Cleaned up existing timers")
    
    # Create a realistic timer session
    print("\n📍 Creating realistic timer session...")
    start_time = timezone.now()
    
    # Create timer
    timer = DocumentTimer.objects.create(
        document=document,
        user=user,
        session_start_time=start_time,
        is_active=True
    )
    print(f"✅ Timer created at: {start_time}")
    
    # Simulate 2 seconds of activity
    time.sleep(2)
    
    # End timer
    end_time = timezone.now()
    timer.session_end_time = end_time
    timer.is_active = False
    timer.ended_by_quiz = True
    timer.save()
    
    print(f"✅ Timer ended at: {end_time}")
    print(f"📊 Total time: {timer.total_time_minutes} minutes")
    
    # Create timing file
    print("\n📁 Creating timing file...")
    timing_file = create_timing_file(document, user)
    
    if timing_file and os.path.exists(timing_file):
        print(f"✅ Timing file created: {timing_file}")
        
        # Read and display file contents
        with open(timing_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"\n📄 File contents:\n{content}")
        
        # Verify file contains expected data
        if str(timer.total_time_minutes) in content:
            print("✅ File contains correct timing data")
        else:
            print("❌ File missing timing data")
            
        if "Ended by Quiz: Yes" in content:
            print("✅ File contains quiz end flag")
        else:
            print("❌ File missing quiz end flag")
            
        return True
    else:
        print("❌ Failed to create timing file")
        return False

def test_multiple_sessions():
    """Test multiple timer sessions for the same document"""
    print("\n🔄 Testing Multiple Timer Sessions...")
    
    user = User.objects.get(username='timer_file_test_user')
    document = Document.objects.get(title='Timer File Test Document', user=user)
    
    # Create second session
    print("\n📍 Creating second timer session...")
    start_time = timezone.now()
    
    timer2 = DocumentTimer.objects.create(
        document=document,
        user=user,
        session_start_time=start_time,
        is_active=True
    )
    
    # Simulate 1 second of activity
    time.sleep(1)
    
    end_time = timezone.now()
    timer2.session_end_time = end_time
    timer2.is_active = False
    timer2.ended_by_quiz = True
    timer2.save()
    
    print(f"✅ Second session: {timer2.total_time_minutes} minutes")
    
    # Create updated timing file
    timing_file = create_timing_file(document, user)
    
    if timing_file and os.path.exists(timing_file):
        with open(timing_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for multiple sessions
        if "Session 1:" in content and "Session 2:" in content:
            print("✅ File contains multiple sessions")
            print(f"\n📄 Updated file contents:\n{content}")
            return True
        else:
            print("❌ File missing multiple sessions")
            return False
    else:
        print("❌ Failed to create updated timing file")
        return False

def check_file_location():
    """Check that files are created in the correct location"""
    print("\n🔍 Checking File Location...")
    
    user = User.objects.get(username='timer_file_test_user')
    expected_dir = f"core/media/timing_files/user_{user.id}"
    
    if os.path.exists(expected_dir):
        print(f"✅ Timing directory exists: {expected_dir}")
        
        files = os.listdir(expected_dir)
        print(f"📁 Files in directory: {files}")
        
        if len(files) > 0:
            print("✅ Timing files are being created")
            return True
        else:
            print("❌ No timing files found")
            return False
    else:
        print(f"❌ Timing directory not found: {expected_dir}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Timer File Creation Tests")
    print("=" * 50)
    
    try:
        # Test basic timer file creation
        test1_success = test_timer_file_creation()
        
        # Test multiple sessions
        test2_success = test_multiple_sessions()
        
        # Check file location
        test3_success = check_file_location()
        
        print("\n" + "=" * 50)
        if test1_success and test2_success and test3_success:
            print("🎉 All timer file tests passed!")
            print("✅ Timer file creation is working correctly")
            print("✅ Multiple sessions are handled properly")
            print("✅ Files are created in correct location")
        else:
            print("❌ Some timer file tests failed")
            print(f"Basic creation: {'✅' if test1_success else '❌'}")
            print(f"Multiple sessions: {'✅' if test2_success else '❌'}")
            print(f"File location: {'✅' if test3_success else '❌'}")
        
        return test1_success and test2_success and test3_success
        
    except Exception as e:
        print(f"\n❌ Timer file tests failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
