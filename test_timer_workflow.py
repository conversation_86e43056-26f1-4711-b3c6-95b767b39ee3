#!/usr/bin/env python3
"""
Test script to simulate the complete timer workflow
"""

import os
import sys
import django
import time
from datetime import datetime, timedelta

# Add the core directory to the path
core_path = os.path.join(os.path.dirname(__file__), 'core')
sys.path.insert(0, core_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from documents.models import Document, DocumentTimer, SummaryTracking
from django.utils import timezone
from documents.api import create_timing_file, create_summary_tracking_file

User = get_user_model()

def simulate_timer_workflow():
    """Simulate a complete timer workflow"""
    print("🔄 Simulating Timer Workflow...")
    
    # Get or create a test user
    user, created = User.objects.get_or_create(
        username='timer_test_user',
        defaults={'email': '<EMAIL>'}
    )
    print(f"✅ Using user: {user.username}")
    
    # Get or create a test document
    document, created = Document.objects.get_or_create(
        title='Timer Test Document',
        user=user,
        defaults={'processing_status': 'completed'}
    )
    print(f"✅ Using document: {document.title}")
    
    # Clean up any existing timers for this test
    DocumentTimer.objects.filter(document=document, user=user).delete()
    print("🧹 Cleaned up existing timers")
    
    # Step 1: Start timer (simulate document load)
    print("\n📍 Step 1: Starting timer (document loaded)")
    start_time = timezone.now()
    timer = DocumentTimer.objects.create(
        document=document,
        user=user,
        session_start_time=start_time,
        is_active=True
    )
    print(f"✅ Timer started at: {start_time}")
    
    # Step 2: Simulate user activity (wait 3 seconds)
    print("\n📍 Step 2: Simulating user activity (3 seconds)")
    time.sleep(3)
    
    # Step 3: Pause timer (simulate navigation away)
    print("\n📍 Step 3: Pausing timer (user navigated away)")
    pause_time = timezone.now()
    timer.pause_time = pause_time
    timer.save()
    print(f"✅ Timer paused at: {pause_time}")
    
    # Step 4: Simulate being away (wait 2 seconds)
    print("\n📍 Step 4: Simulating time away (2 seconds)")
    time.sleep(2)
    
    # Step 5: Resume timer (simulate return to document)
    print("\n📍 Step 5: Resuming timer (user returned)")
    resume_time = timezone.now()
    timer.resume_time = resume_time
    timer.pause_time = None  # Clear pause time
    timer.save()
    print(f"✅ Timer resumed at: {resume_time}")
    
    # Step 6: Simulate more activity (wait 2 seconds)
    print("\n📍 Step 6: Simulating more user activity (2 seconds)")
    time.sleep(2)
    
    # Step 7: Stop timer (simulate quiz start)
    print("\n📍 Step 7: Stopping timer (quiz started)")
    end_time = timezone.now()
    timer.session_end_time = end_time
    timer.is_active = False
    timer.ended_by_quiz = True
    timer.save()
    print(f"✅ Timer stopped at: {end_time}")
    
    # Calculate and display results
    total_time = timer.total_time_minutes
    print(f"\n📊 Results:")
    print(f"   Start Time: {timer.session_start_time}")
    print(f"   End Time: {timer.session_end_time}")
    print(f"   Total Time: {total_time} minutes")
    print(f"   Expected Time: ~5 minutes (3+2 seconds of activity)")
    
    # Create timing file
    print("\n📁 Creating timing file...")
    timing_file = create_timing_file(document, user)
    if timing_file:
        print(f"✅ Timing file created: {timing_file}")
        # Display file contents
        with open(timing_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"\n📄 File contents:\n{content}")
    else:
        print("❌ Failed to create timing file")
    
    return timer

def simulate_summary_tracking():
    """Simulate summary tracking workflow"""
    print("\n🔄 Simulating Summary Tracking Workflow...")
    
    # Get test user and document
    user = User.objects.get(username='timer_test_user')
    document = Document.objects.get(title='Timer Test Document', user=user)
    
    # Clean up existing tracking
    SummaryTracking.objects.filter(document=document, user=user).delete()
    print("🧹 Cleaned up existing summary tracking")
    
    # Create summary tracking with all events
    print("\n📍 Creating summary tracking with events...")
    tracking = SummaryTracking.objects.create(
        document=document,
        user=user,
        first_view_time=timezone.now() - timedelta(minutes=5),
        reached_end=True,
        reached_end_at=timezone.now() - timedelta(minutes=2),
        stayed_avg_time=True,
        stayed_avg_time_at=timezone.now() - timedelta(minutes=1),
        reopened_once=True,
        reopened_at=timezone.now(),
        view_count=3,
        predicted_avg_time_minutes=4.5,
        total_view_time_minutes=6.2
    )
    print("✅ Summary tracking created with all events")
    
    # Create summary tracking file
    print("\n📁 Creating summary tracking file...")
    summary_file = create_summary_tracking_file(document, user, tracking)
    if summary_file:
        print(f"✅ Summary tracking file created: {summary_file}")
        # Display file contents
        with open(summary_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"\n📄 File contents:\n{content}")
    else:
        print("❌ Failed to create summary tracking file")
    
    return tracking

def main():
    """Main test function"""
    print("🚀 Starting Complete Timer and Summary Tracking Workflow Test")
    print("=" * 70)
    
    try:
        # Test timer workflow
        timer = simulate_timer_workflow()
        
        # Test summary tracking workflow
        tracking = simulate_summary_tracking()
        
        print("\n" + "=" * 70)
        print("🎉 Complete workflow test completed successfully!")
        print("✅ Timer workflow is working correctly")
        print("✅ Summary tracking workflow is working correctly")
        print("✅ File creation is working correctly")
        print(f"✅ Timer recorded: {timer.total_time_minutes} minutes")
        print(f"✅ Summary tracking recorded: {tracking.view_count} views")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Workflow test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
